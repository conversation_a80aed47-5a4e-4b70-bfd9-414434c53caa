import sinon from 'sinon';
import {Client, expect} from '@loopback/testlab';
import {
  ConfigureDeviceRepository,
  PlanRepository,
  SubscriptionRepository,
} from '../../repositories';
import {getToken, setupApplication} from './test-helper';
import {STATUS_CODE} from '@sourceloop/core';
import {SubscriptionServiceApplication} from '../../application';
import {ConfigureDevice} from '../../models';
import {PermissionKey} from '@local/core';

describe('ConfigureDeviceController', () => {
  let app: SubscriptionServiceApplication;
  let client: Client;
  let configureDeviceRepository: sinon.SinonStubbedInstance<ConfigureDeviceRepository>;
  let planRepository: sinon.SinonStubbedInstance<PlanRepository>;
  let subscriptionRepository: sinon.SinonStubbedInstance<SubscriptionRepository>;

  const mockConfigureDevice: ConfigureDevice = {
    id: '12345',
    min: 10,
    max: 100,
    createdOn: new Date().toISOString(),
    modifiedOn: new Date().toISOString(),
    deleted: false,
  } as unknown as ConfigureDevice;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(async () => {
    configureDeviceRepository = {
      create: sinon.stub().resolves(mockConfigureDevice),
      find: sinon.stub().resolves([mockConfigureDevice]),
      updateAll: sinon.stub().resolves({count: 1}),
      deleteById: sinon.stub().resolves(),
      exists: sinon.stub().resolves(false),
      updateById: sinon.stub().resolves(),
      deleteAll: sinon.stub().resolves({count: 1}),
    } as unknown as sinon.SinonStubbedInstance<ConfigureDeviceRepository>;

    planRepository = {
      findAll: sinon.stub().resolves([]),
      deleteAll: sinon.stub().resolves({count: 1}),
    } as unknown as sinon.SinonStubbedInstance<PlanRepository>;

    subscriptionRepository = {
      find: sinon.stub().resolves([]),
    } as unknown as sinon.SinonStubbedInstance<SubscriptionRepository>;

    app
      .bind('repositories.ConfigureDeviceRepository')
      .to(configureDeviceRepository);
    app.bind('repositories.PlanRepository').to(planRepository);
    app.bind('repositories.SubscriptionRepository').to(subscriptionRepository);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('invokes POST /configure-devices with valid token', async () => {
    const token = getToken([PermissionKey.CreateConfigureDevices]);
    const {body} = await client
      .post('/configure-devices')
      .set('Authorization', token)
      .send({
        min: 10,
        max: 100,
      })
      .expect(STATUS_CODE.OK);

    expect(body).to.containEql({
      id: mockConfigureDevice.id,
      min: mockConfigureDevice.min,
      max: mockConfigureDevice.max,
    });
  });

  it('invokes GET /configure-devices with valid token', async () => {
    const token = getToken([PermissionKey.ViewConfigureDevices]);
    const {body} = await client
      .get('/configure-devices')
      .set('Authorization', token)
      .expect(STATUS_CODE.OK);

    expect(body).to.be.Array();
    expect(body[0]).to.containEql({
      id: mockConfigureDevice.id,
      min: mockConfigureDevice.min,
      max: mockConfigureDevice.max,
    });
  });

  it('invokes PATCH /configure-devices with valid token', async () => {
    const token = getToken([PermissionKey.UpdateConfigureDevices]);
    const {body} = await client
      .patch('/configure-devices')
      .set('Authorization', token)
      .send({max: 200})
      .expect(STATUS_CODE.OK);

    expect(body).to.have.property('count', 1);
  });

  it('invokes DELETE /configure-devices/{id} with valid token', async () => {
    const token = getToken([PermissionKey.DeleteConfigureDevices]);
    await client
      .del(`/configure-devices/${mockConfigureDevice.id}`)
      .set('Authorization', token)
      .expect(STATUS_CODE.NO_CONTENT);
  });

  describe('POST /configure-devices/upsert-batch', () => {
    it('should create new devices when no IDs provided', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {min: 1, max: 10, computeSize: 'small'},
        {min: 5, max: 50, computeSize: 'medium'},
      ];

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should create new devices when IDs do not exist', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {id: 'non-existent-1', min: 1, max: 10},
        {id: 'non-existent-2', min: 5, max: 50},
      ];

      // Mock that devices don't exist
      (configureDeviceRepository.exists as sinon.SinonStub).resolves(false);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should update existing devices when no active plans exist', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {id: 'existing-1', min: 2, max: 20},
        {id: 'existing-2', min: 10, max: 100},
      ];

      // Mock that devices exist but no active plans
      (configureDeviceRepository.exists as sinon.SinonStub).resolves(true);
      (planRepository.findAll as sinon.SinonStub).resolves([]);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should update existing devices when active plans exist but no subscriptions', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {id: 'existing-1', min: 2, max: 20},
      ];

      // Mock that device exists with active plans but no subscriptions
      (configureDeviceRepository.exists as sinon.SinonStub).resolves(true);
      (planRepository.findAll as sinon.SinonStub).resolves([{id: 'plan-1'}]);
      (subscriptionRepository.find as sinon.SinonStub).resolves([]);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should return error when device has active plans with subscriptions', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {id: 'existing-with-subscription', min: 2, max: 20},
      ];

      // Mock that device exists with active plans and subscriptions
      (configureDeviceRepository.exists as sinon.SinonStub).resolves(true);
      (planRepository.findAll as sinon.SinonStub).resolves([{id: 'plan-1'}]);
      (subscriptionRepository.find as sinon.SinonStub).resolves([{id: 'sub-1'}]);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', false);
      expect(body).to.have.property('error', 'Cannot update configure device as it is associated with a subscription');
    });

    it('should delete device and associated plans when device.deleted is true', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {id: 'existing-to-delete', deleted: true},
      ];

      // Mock that device exists with no active plans
      (configureDeviceRepository.exists as sinon.SinonStub).resolves(true);
      (planRepository.findAll as sinon.SinonStub).resolves([]);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should handle mixed operations in single batch', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices = [
        {min: 1, max: 10}, // Create new
        {id: 'existing-1', min: 2, max: 20}, // Update existing
        {id: 'existing-2', deleted: true}, // Delete existing
      ];

      // Mock different scenarios for different devices
      (configureDeviceRepository.exists as sinon.SinonStub)
        .onFirstCall().resolves(true) // existing-1 exists
        .onSecondCall().resolves(true); // existing-2 exists
      (planRepository.findAll as sinon.SinonStub).resolves([]);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should handle empty array gracefully', async () => {
      const token = getToken([PermissionKey.UpdateConfigureDevices]);
      const devices: any[] = [];

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.OK);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');
    });

    it('should return 401 when no authorization token provided', async () => {
      const devices = [{min: 1, max: 10}];

      await client
        .post('/configure-devices/upsert-batch')
        .send(devices)
        .expect(STATUS_CODE.UNAUTHORISED);
    });

    it('should return 403 when insufficient permissions', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]); // Wrong permission
      const devices = [{min: 1, max: 10}];

      await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(STATUS_CODE.FORBIDDEN);
    });
  });
});
