import {PlanStatus} from '@local/core';
import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ConfigureDevice} from '../models';
import {
  ConfigureDeviceRepository,
  PlanRepository,
  SubscriptionRepository,
} from '../repositories';
import {IManageDeviceLimitService} from '../types';

@injectable({scope: BindingScope.REQUEST})
export class ManageDeviceLimitService implements IManageDeviceLimitService {
  /**
   * Constructs a new instance of the ManageDeviceLimitService.
   * @param {ConfigureDeviceRepository} configureDeviceRepository - Repository for managing configure devices.
   * @param {PlanRepository} planRepository - Repository for managing plans.
   * @param {SubscriptionRepository} subscriptionRepository - Repository for managing subscriptions.
   */
  constructor(
    @repository(ConfigureDeviceRepository)
    private configureDeviceRepository: ConfigureDeviceRepository,
    @repository(PlanRepository)
    private planRepository: PlanRepository,
    @repository(SubscriptionRepository)
    private subscriptionRepository: SubscriptionRepository,
  ) {}

  /**
   * Upserts a batch of configure devices.
   * For each device, if it exists and has an active plan with subscriptions,
   * it throws an error. Otherwise, it updates or creates the device.
   *
   * @param devices - Array of partial ConfigureDevice objects to upsert
   * @returns Promise with success status and optional error message
   */
  async upsertDeviceBatch(
    devices: Partial<ConfigureDevice>[],
  ): Promise<{success: boolean; error?: string}> {
    try {
      for (const device of devices) {
        if (!device.id) {
          // No ID → always create
          await this.configureDeviceRepository.create(device);
          continue;
        }

        const exists = await this.configureDeviceRepository.exists(device.id);
        if (!exists) {
          await this.configureDeviceRepository.create(device);
          continue;
        }

        // Device exists → check subscriptions if it's ACTIVE
        const activePlans = await this.planRepository.findAll({
          where: {configureDeviceId: device.id, status: PlanStatus.ACTIVE},
          fields: {id: true}, // only fetch what's needed
        });

        if (activePlans.length > 0) {
          const planIds = activePlans.map(plan => plan.id);
          const subscriptions = await this.subscriptionRepository.find({
            where: {planId: {inq: planIds}},
            limit: 1, // just need to know if any exist
          });

          if (subscriptions.length > 0) {
            throw new Error(
              'Cannot update configure device as it is associated with a subscription',
            );
          }
        }

        // Handle delete vs update
        if (device.deleted) {
          await this.planRepository.deleteAll({configureDeviceId: device.id});
          await this.configureDeviceRepository.deleteById(device.id);
        } else {
          await this.configureDeviceRepository.updateById(device.id, device);
        }
      }

      return {success: true};
    } catch (error) {
      return {success: false, error: error?.message || 'Unknown error'};
    }
  }
}
