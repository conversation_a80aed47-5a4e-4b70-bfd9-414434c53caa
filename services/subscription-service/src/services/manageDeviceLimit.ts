import {PlanStatus} from '@local/core';
import {BindingScope, injectable} from '@loopback/core';
import {repository} from '@loopback/repository';
import {ConfigureDevice} from '../models';
import {
  ConfigureDeviceRepository,
  PlanRepository,
  SubscriptionRepository,
} from '../repositories';

@injectable({scope: BindingScope.REQUEST})
export class ManageDeviceLimitService {
  /**
   * Constructs a new instance of the ManageDeviceLimitService.
   * @param {ConfigureDeviceRepository} configureDeviceRepository - Repository for managing configure devices.
   * @param {PlanRepository} planRepository - Repository for managing plans.
   * @param {SubscriptionRepository} subscriptionRepository - Repository for managing subscriptions.
   */
  constructor(
    @repository(ConfigureDeviceRepository)
    private configureDeviceRepository: ConfigureDeviceRepository,
    @repository(PlanRepository)
    private planRepository: PlanRepository,
    @repository(SubscriptionRepository)
    private subscriptionRepository: SubscriptionRepository,
  ) {}

  /**
   * Upserts a batch of configure devices.
   * For each device, if it exists and has an active plan with subscriptions,
   * it throws an error. Otherwise, it updates or creates the device.
   *
   * @param devices - Array of partial ConfigureDevice objects to upsert
   * @returns Promise with success status and optional error message
   */
  async upsertDeviceBatch(
    devices: Partial<ConfigureDevice>[],
  ): Promise<{success: boolean; error?: string}> {
    try {
      for (const device of devices) {
        if (device.id) {
          const exists = await this.configureDeviceRepository.exists(device.id);
          if (exists) {
            const planRecord = await this.planRepository.findOne({
              where: {configureDeviceId: device.id},
            });
            if (planRecord && planRecord.status === PlanStatus.ACTIVE) {
              const subscriptionRecord = await this.subscriptionRepository.find({
                where: {planId: planRecord?.id},
              });
              if (subscriptionRecord.length > 0) {
                throw new Error(
                  'Cannot update configure device as it is associated with a subscription',
                );
              }
            }
            await this.configureDeviceRepository.updateById(device.id, device);
          } else {
            await this.configureDeviceRepository.create(device);
          }
        } else {
          await this.configureDeviceRepository.create(device);
        }
      }
      return {success: true};
    } catch (error) {
      return {success: false, error: error?.message || 'Unknown error'};
    }
  }
}