import {Client, expect, sinon} from '@loopback/testlab';
import {getToken, setupApplication} from './test-helper';
import {PermissionKey} from '@local/core';
import {TenantMgmtFacadeApplication} from '../../application';
import {SubscriptionProxyService} from '../../services/proxies';
import {ConfigureDevice} from '../../models';

describe('ConfigureDeviceController', () => {
  let app: TenantMgmtFacadeApplication;
  let client: Client;
  let subscriptionServiceProxyServiceStub: sinon.SinonStubbedInstance<SubscriptionProxyService>;

  before('setupApplication', async () => {
    ({app, client} = await setupApplication());
  });

  after(async () => {
    await app.stop();
  });

  beforeEach(() => {
    subscriptionServiceProxyServiceStub = {
      updateSubscription: sinon.stub(),
      createInvoice: sinon.stub(),
      findPlanById: sinon.stub(),
      getConfigureDevices: sinon.stub(),
      getStorageSize: sinon.stub(),
      getSubscriptionTenure: sinon.stub(),
      getPlans: sinon.stub(),
      createPlan: sinon.stub(),
      getCurrencies: sinon.stub(),
      getPlansCount: sinon.stub(),
      getPlanHistory: sinon.stub(),
      getAllPlanStatus: sinon.stub(),
      createSubscriptions: sinon.stub(),
      updatePlanById: sinon.stub(), // Added stub for updatePlanById
      createPlanHistory: sinon.stub(), // Added stub for createPlanHistory
      createPrice: sinon.stub(), // Added stub for createPrice
      createCustomer: sinon.stub(),
      getTenantBillings: sinon.stub(),
      getTenantBillingsCount: sinon.stub(),
      getAllBillingStatus: sinon.stub(),
      handleWebhook: sinon.stub(),
      getInvoices: sinon.stub(),
      getInvoicesCount: sinon.stub(),
      getInvoiceById: sinon.stub(),
      getSubscriptions: sinon.stub(),
      updateSubscriptionById: sinon.stub(),
      getStripeInvoicePdfUrl: sinon.stub(),
      updateConfigureDevices: sinon.stub(),
    };
    app
      .bind('services.SubscriptionProxyService')
      .to(subscriptionServiceProxyServiceStub);
  });

  afterEach(() => {
    sinon.restore();
  });

  it('returns list of configure devices when authorized', async () => {
    const token = getToken([PermissionKey.ViewConfigureDevices]);

    const mockDevices: ConfigureDevice[] = [
      new ConfigureDevice({min: 1, max: 5}),
    ];
    subscriptionServiceProxyServiceStub.getConfigureDevices.resolves(
      mockDevices,
    );

    const res = await client
      .get('/configure-devices')
      .set('Authorization', token)
      .expect(200);
    expect(res.body).to.deepEqual(
      mockDevices.map(d => (d.toJSON ? d.toJSON() : {...d})),
    );
  });

  it('returns empty array when no devices found', async () => {
    subscriptionServiceProxyServiceStub.getConfigureDevices.resolves([]);

    const token = getToken([PermissionKey.ViewConfigureDevices]);

    const res = await client
      .get('/configure-devices')
      .set('Authorization', token)
      .expect(200);

    expect(res.body).to.deepEqual([]);
  });

  it('fails with 401 when no token is provided', async () => {
    await client.get('/configure-devices').expect(401);
    sinon.assert.notCalled(
      subscriptionServiceProxyServiceStub.getConfigureDevices,
    );
  });

  describe('POST /configure-devices/upsert-batch', () => {
    it('should successfully upsert devices when authorized', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]);
      const devices = [
        {min: 1, max: 10, computeSize: 'small'},
        {min: 5, max: 50, computeSize: 'medium'},
      ];

      const mockResponse = {success: true};
      subscriptionServiceProxyServiceStub.updateConfigureDevices.resolves(mockResponse);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(200);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');

      sinon.assert.calledOnce(subscriptionServiceProxyServiceStub.updateConfigureDevices);
      sinon.assert.calledWith(
        subscriptionServiceProxyServiceStub.updateConfigureDevices,
        token,
        sinon.match.any
      );
    });

    it('should handle service errors gracefully', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]);
      const devices = [
        {id: 'existing-with-subscription', min: 2, max: 20},
      ];

      const mockErrorResponse = {
        success: false,
        error: 'Cannot update configure device as it is associated with a subscription'
      };
      subscriptionServiceProxyServiceStub.updateConfigureDevices.resolves(mockErrorResponse);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(200);

      expect(body).to.have.property('success', false);
      expect(body).to.have.property('error', 'Cannot update configure device as it is associated with a subscription');

      sinon.assert.calledOnce(subscriptionServiceProxyServiceStub.updateConfigureDevices);
    });

    it('should handle empty device array', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]);
      const devices: any[] = [];

      const mockResponse = {success: true};
      subscriptionServiceProxyServiceStub.updateConfigureDevices.resolves(mockResponse);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(200);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');

      sinon.assert.calledOnce(subscriptionServiceProxyServiceStub.updateConfigureDevices);
      sinon.assert.calledWith(
        subscriptionServiceProxyServiceStub.updateConfigureDevices,
        token,
        sinon.match.any
      );
    });

    it('should handle mixed device operations', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]);
      const devices = [
        {min: 1, max: 10}, // Create new
        {id: 'existing-1', min: 2, max: 20}, // Update existing
        {id: 'existing-2', deleted: true}, // Delete existing
      ];

      const mockResponse = {success: true};
      subscriptionServiceProxyServiceStub.updateConfigureDevices.resolves(mockResponse);

      const {body} = await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(200);

      expect(body).to.have.property('success', true);
      expect(body).to.not.have.property('error');

      sinon.assert.calledOnce(subscriptionServiceProxyServiceStub.updateConfigureDevices);
      sinon.assert.calledWith(
        subscriptionServiceProxyServiceStub.updateConfigureDevices,
        token,
        devices
      );
    });

    it('should pass authorization token correctly', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]);
      const devices = [{min: 1, max: 10}];

      const mockResponse = {success: true};
      subscriptionServiceProxyServiceStub.updateConfigureDevices.resolves(mockResponse);

      await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(200);

      // Verify that the exact token was passed to the service
      sinon.assert.calledWith(
        subscriptionServiceProxyServiceStub.updateConfigureDevices,
        token,
        devices
      );
    });

    it('should return 401 when no authorization token provided', async () => {
      const devices = [{min: 1, max: 10}];

      await client
        .post('/configure-devices/upsert-batch')
        .send(devices)
        .expect(401);

      sinon.assert.notCalled(subscriptionServiceProxyServiceStub.updateConfigureDevices);
    });

    it('should return 403 when insufficient permissions', async () => {
      const token = getToken([PermissionKey.CreateTenant]); // Wrong permission
      const devices = [{min: 1, max: 10}];

      await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(403);

      sinon.assert.notCalled(subscriptionServiceProxyServiceStub.updateConfigureDevices);
    });

    it('should handle proxy service rejection', async () => {
      const token = getToken([PermissionKey.ViewConfigureDevices]);
      const devices = [{min: 1, max: 10}];

      subscriptionServiceProxyServiceStub.updateConfigureDevices.rejects(
        new Error('Subscription service unavailable')
      );

      await client
        .post('/configure-devices/upsert-batch')
        .set('Authorization', token)
        .send(devices)
        .expect(500);

      sinon.assert.calledOnce(subscriptionServiceProxyServiceStub.updateConfigureDevices);
    });
  });
});
